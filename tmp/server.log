2025-07-28 16:58:59 - 0.7.3_00000000000000 - core.providers.llm.openai.openai - ERROR - core.providers.llm.openai.openai - 配置错误: LLM 的 API key 未设置,当前值为: 你的chat-glm web key
2025-07-28 16:58:59 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: llm成功 ChatGLMLLM
2025-07-28 16:58:59 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: intent成功 function_call
2025-07-28 16:58:59 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: memory成功 nomem
2025-07-28 16:59:06 - 0.7.3_00000000000000 - core.providers.vad.silero - INFO - core.providers.vad.silero - SileroVAD
2025-07-28 16:59:07 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: vad成功 SileroVAD
2025-07-28 16:59:39 - 0.7.3_00000000000000 - core.providers.asr.fun_local - INFO - core.providers.asr.fun_local - funasr version: 1.2.3.
2025-07-28 16:59:39 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - ASR模块初始化完成
2025-07-28 16:59:39 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: asr成功 FunASR
2025-07-28 16:59:39 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - OTA接口是		http://10.1.1.20:9904/xiaozhi/ota/
2025-07-28 16:59:39 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - 视觉分析接口是	http://10.1.1.20:9904/mcp/vision/explain
2025-07-28 16:59:39 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - 健康检查接口是	http://10.1.1.20:9902/k8s_liveiness (存活性探针)
2025-07-28 16:59:39 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - 健康检查接口是	http://10.1.1.20:9902/k8s_readiness (就绪性探针)
2025-07-28 16:59:39 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - Websocket地址是	ws://10.1.1.20:9903/xiaozhi/v1/
2025-07-28 16:59:39 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - =======上面的地址是websocket协议地址，请勿用浏览器访问=======
2025-07-28 16:59:39 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - 如想测试websocket请用谷歌浏览器打开test目录下的test_page.html
2025-07-28 16:59:39 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - =============================================================

2025-07-28 16:59:39 - 0.7.3_00000000000000 - core.http_server - INFO - core.http_server - 主HTTP服务器启动在 0.0.0.0:9904
2025-07-28 16:59:39 - 0.7.3_00000000000000 - core.http_server - INFO - core.http_server - 健康检查服务器启动在 0.0.0.0:9902
2025-07-28 17:28:31 - 0.7.3_00000000000000 - core.providers.llm.openai.openai - ERROR - core.providers.llm.openai.openai - 配置错误: LLM 的 API key 未设置,当前值为: 你的chat-glm web key
2025-07-28 17:28:31 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: llm成功 ChatGLMLLM
2025-07-28 17:28:31 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: intent成功 function_call
2025-07-28 17:28:31 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: memory成功 nomem
2025-07-28 17:28:35 - 0.7.3_00000000000000 - core.providers.vad.silero - INFO - core.providers.vad.silero - SileroVAD
2025-07-28 17:28:35 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: vad成功 SileroVAD
2025-07-28 17:28:55 - 0.7.3_00000000000000 - core.providers.asr.fun_local - INFO - core.providers.asr.fun_local - funasr version: 1.2.3.
2025-07-28 17:28:55 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - ASR模块初始化完成
2025-07-28 17:28:55 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: asr成功 FunASR
2025-07-28 17:28:55 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - OTA接口是		http://10.1.1.20:9904/xiaozhi/ota/
2025-07-28 17:28:55 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - 视觉分析接口是	http://10.1.1.20:9904/mcp/vision/explain
2025-07-28 17:28:55 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - 健康检查接口是	http://10.1.1.20:9902/k8s_liveiness (存活性探针)
2025-07-28 17:28:55 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - 健康检查接口是	http://10.1.1.20:9902/k8s_readiness (就绪性探针)
2025-07-28 17:28:55 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - Websocket地址是	ws://10.1.1.20:9903/xiaozhi/v1/
2025-07-28 17:28:55 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - =======上面的地址是websocket协议地址，请勿用浏览器访问=======
2025-07-28 17:28:55 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - 如想测试websocket请用谷歌浏览器打开test目录下的test_page.html
2025-07-28 17:28:55 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - =============================================================

2025-07-28 17:28:55 - 0.7.3_00000000000000 - core.http_server - INFO - core.http_server - 主HTTP服务器启动在 0.0.0.0:9904
2025-07-28 17:28:55 - 0.7.3_00000000000000 - core.http_server - INFO - core.http_server - 健康检查服务器启动在 0.0.0.0:9902
2025-07-28 17:36:02 - 0.7.3_00000000000000 - core.providers.llm.openai.openai - ERROR - core.providers.llm.openai.openai - 配置错误: LLM 的 API key 未设置,当前值为: 你的chat-glm web key
2025-07-28 17:36:02 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: llm成功 ChatGLMLLM
2025-07-28 17:36:02 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: intent成功 function_call
2025-07-28 17:36:02 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: memory成功 nomem
2025-07-28 17:36:07 - 0.7.3_00000000000000 - core.providers.vad.silero - INFO - core.providers.vad.silero - SileroVAD
2025-07-28 17:36:07 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: vad成功 SileroVAD
2025-07-28 17:36:26 - 0.7.3_00000000000000 - core.providers.asr.fun_local - INFO - core.providers.asr.fun_local - funasr version: 1.2.3.
2025-07-28 17:36:26 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - ASR模块初始化完成
2025-07-28 17:36:26 - 0.7.3_00000000000000 - core.utils.modules_initialize - INFO - core.utils.modules_initialize - 初始化组件: asr成功 FunASR
2025-07-28 17:36:26 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - OTA接口是		http://10.1.1.20:9904/xiaozhi/ota/
2025-07-28 17:36:26 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - 视觉分析接口是	http://10.1.1.20:9904/mcp/vision/explain
2025-07-28 17:36:26 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - 健康检查接口是	http://10.1.1.20:9902/k8s_liveiness (存活性探针)
2025-07-28 17:36:26 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - 健康检查接口是	http://10.1.1.20:9902/k8s_readiness (就绪性探针)
2025-07-28 17:36:26 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - Websocket地址是	ws://10.1.1.20:9903/xiaozhi/v1/
2025-07-28 17:36:26 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - =======上面的地址是websocket协议地址，请勿用浏览器访问=======
2025-07-28 17:36:26 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - 如想测试websocket请用谷歌浏览器打开test目录下的test_page.html
2025-07-28 17:36:26 - 0.7.3_00000000000000 - __main__ - INFO - __main__ - =============================================================

2025-07-28 17:36:26 - 0.7.3_00000000000000 - core.http_server - INFO - core.http_server - 主HTTP服务器启动在 0.0.0.0:9904
2025-07-28 17:36:26 - 0.7.3_00000000000000 - core.http_server - INFO - core.http_server - 健康检查服务器启动在 0.0.0.0:9902
