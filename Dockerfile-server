# 第一阶段：构建Python依赖
FROM python:3.10-alpine AS builder

# 安装构建依赖
RUN apk add --no-cache \
    gcc \
    musl-dev \
    linux-headers \
    libffi-dev \
    openssl-dev \
    cargo \
    rust \
    pkgconfig \
    opus-dev \
    ffmpeg-dev

WORKDIR /app

# 复制requirements文件
COPY requirements.txt .

# 创建虚拟环境并安装依赖
RUN python -m venv /opt/venv && \
    . /opt/venv/bin/activate && \
    pip install --no-cache-dir --upgrade pip setuptools wheel && \
    pip install --no-cache-dir -r requirements.txt && \
    # 清理不必要的文件
    find /opt/venv -name "*.pyc" -delete && \
    find /opt/venv -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true && \
    find /opt/venv -name "*.pyo" -delete

# 第二阶段：运行时镜像
FROM python:3.10-alpine

# 安装运行时依赖
RUN apk add --no-cache \
    opus \
    ffmpeg \
    libffi \
    openssl && \
    # 创建非root用户
    addgroup -g 1000 appuser && \
    adduser -u 1000 -G appuser -s /bin/sh -D appuser

WORKDIR /opt/xiaozhi-esp32-server

# 从构建阶段复制虚拟环境
COPY --from=builder /opt/venv /opt/venv

# 确保使用虚拟环境
ENV PATH="/opt/venv/bin:$PATH"

# 复制应用代码（只复制必要文件）
COPY --chown=appuser:appuser app.py .
COPY --chown=appuser:appuser config/ ./config/
COPY --chown=appuser:appuser core/ ./core/
COPY --chown=appuser:appuser plugins_func/ ./plugins_func/
COPY --chown=appuser:appuser agent-base-prompt.txt .
COPY --chown=appuser:appuser config.yaml .
COPY --chown=appuser:appuser mcp_server_settings.json .

# 创建必要的目录
RUN mkdir -p tmp data models && \
    chown -R appuser:appuser /opt/xiaozhi-esp32-server

# 切换到非root用户
USER appuser

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import requests; requests.get('http://localhost:8000/health', timeout=5)" || exit 1

# 启动应用
CMD ["python", "app.py"]