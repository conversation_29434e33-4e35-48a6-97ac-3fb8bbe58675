#!/usr/bin/env python3
"""
健康检查端点测试脚本
用于测试Kubernetes健康检查端点是否正常工作
"""

import asyncio
import aiohttp
import json
import sys
from config.settings import load_config


async def test_health_endpoints():
    """测试健康检查端点"""
    try:
        # 加载配置
        config = load_config()
        server_config = config.get("server", {})
        host = server_config.get("ip", "0.0.0.0")
        health_port = int(server_config.get("health_port", 9902))
        
        # 如果host是0.0.0.0，改为localhost进行测试
        if host == "0.0.0.0":
            host = "localhost"
        
        base_url = f"http://{host}:{health_port}"
        
        print(f"测试健康检查端点: {base_url}")
        print("=" * 50)
        
        async with aiohttp.ClientSession() as session:
            # 测试存活性探针
            print("1. 测试存活性探针 (/k8s_liveiness)")
            try:
                async with session.get(f"{base_url}/k8s_liveiness") as response:
                    status = response.status
                    text = await response.text()
                    
                    print(f"   状态码: {status}")
                    print(f"   响应头: {dict(response.headers)}")
                    
                    try:
                        data = json.loads(text)
                        print(f"   响应内容: {json.dumps(data, ensure_ascii=False, indent=2)}")
                    except json.JSONDecodeError:
                        print(f"   响应内容: {text}")
                    
                    if status == 200:
                        print("   ✅ 存活性探针测试通过")
                    else:
                        print("   ❌ 存活性探针测试失败")
                        
            except Exception as e:
                print(f"   ❌ 存活性探针测试异常: {e}")
            
            print()
            
            # 测试就绪性探针
            print("2. 测试就绪性探针 (/k8s_readiness)")
            try:
                async with session.get(f"{base_url}/k8s_readiness") as response:
                    status = response.status
                    text = await response.text()
                    
                    print(f"   状态码: {status}")
                    print(f"   响应头: {dict(response.headers)}")
                    
                    try:
                        data = json.loads(text)
                        print(f"   响应内容: {json.dumps(data, ensure_ascii=False, indent=2)}")
                    except json.JSONDecodeError:
                        print(f"   响应内容: {text}")
                    
                    if status == 200:
                        print("   ✅ 就绪性探针测试通过")
                    else:
                        print("   ⚠️  就绪性探针返回非200状态码（这在服务未完全启动时是正常的）")
                        
            except Exception as e:
                print(f"   ❌ 就绪性探针测试异常: {e}")
            
            print()
            print("=" * 50)
            print("测试完成")
            
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        sys.exit(1)


async def test_health_endpoints_standalone():
    """独立测试健康检查逻辑（不需要服务器运行）"""
    print("独立测试健康检查逻辑")
    print("=" * 50)
    
    try:
        from config.settings import load_config
        from core.api.health_handler import HealthHandler
        from aiohttp import web
        
        # 加载配置
        config = load_config()
        
        # 创建健康检查处理器
        health_handler = HealthHandler(config)
        
        # 模拟请求对象
        class MockRequest:
            pass
        
        mock_request = MockRequest()
        
        # 测试存活性检查
        print("1. 测试存活性检查逻辑")
        try:
            response = await health_handler.handle_liveness(mock_request)
            print(f"   状态码: {response.status}")
            print(f"   内容类型: {response.content_type}")
            
            # 读取响应内容
            body = response.body
            if body:
                try:
                    data = json.loads(body.decode('utf-8'))
                    print(f"   响应内容: {json.dumps(data, ensure_ascii=False, indent=2)}")
                except:
                    print(f"   响应内容: {body.decode('utf-8')}")
            
            if response.status == 200:
                print("   ✅ 存活性检查逻辑正常")
            else:
                print("   ❌ 存活性检查逻辑异常")
                
        except Exception as e:
            print(f"   ❌ 存活性检查逻辑测试异常: {e}")
        
        print()
        
        # 测试就绪性检查
        print("2. 测试就绪性检查逻辑")
        try:
            response = await health_handler.handle_readiness(mock_request)
            print(f"   状态码: {response.status}")
            print(f"   内容类型: {response.content_type}")
            
            # 读取响应内容
            body = response.body
            if body:
                try:
                    data = json.loads(body.decode('utf-8'))
                    print(f"   响应内容: {json.dumps(data, ensure_ascii=False, indent=2)}")
                except:
                    print(f"   响应内容: {body.decode('utf-8')}")
            
            if response.status in [200, 503]:
                print("   ✅ 就绪性检查逻辑正常")
            else:
                print("   ❌ 就绪性检查逻辑异常")
                
        except Exception as e:
            print(f"   ❌ 就绪性检查逻辑测试异常: {e}")
        
        print()
        print("=" * 50)
        print("独立测试完成")
        
    except Exception as e:
        print(f"独立测试过程中发生错误: {e}")


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "standalone":
        # 独立测试模式
        asyncio.run(test_health_endpoints_standalone())
    else:
        # 端点测试模式（需要服务器运行）
        print("注意: 此测试需要服务器正在运行")
        print("如果服务器未运行，请先启动服务器: python app.py")
        print("如果要进行独立逻辑测试，请运行: python test_health_check.py standalone")
        print()
        asyncio.run(test_health_endpoints())
