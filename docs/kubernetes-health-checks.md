# Kubernetes 健康检查实现文档

## 概述

本项目已实现了 Kubernetes 健康检查端点，用于支持 liveness probe（存活性探针）和 readiness probe（就绪性探针）。

## 实现的端点

### 1. 存活性探针 (Liveness Probe)
- **端点**: `/k8s_liveiness`
- **端口**: 9902 (可配置)
- **用途**: 检查应用程序是否还在运行
- **失败后果**: Kubernetes 会重启容器

### 2. 就绪性探针 (Readiness Probe)  
- **端点**: `/k8s_readiness`
- **端口**: 9902 (可配置)
- **用途**: 检查应用程序是否准备好接收流量
- **失败后果**: Kubernetes 会将 Pod 从 Service 的端点中移除

## 配置

### 配置文件修改

在 `config.yaml` 和 `config_from_api.yaml` 中添加了健康检查端口配置：

```yaml
server:
  ip: 0.0.0.0
  port: 8000
  http_port: 8003
  health_port: 9902  # 新增：健康检查端口
```

### Kubernetes 配置示例

```yaml
livenessProbe:
  httpGet:
    path: /k8s_liveiness
    port: 9902
  initialDelaySeconds: 121
  timeoutSeconds: 10
readinessProbe:
  httpGet:
    path: /k8s_readiness
    port: 9902
  periodSeconds: 10
  timeoutSeconds: 10
```

## 实现细节

### 文件结构

1. **`core/api/health_handler.py`** - 健康检查处理器
2. **`core/http_server.py`** - 修改后的HTTP服务器，支持健康检查端口
3. **`app.py`** - 主应用入口，集成健康检查服务

### 健康检查逻辑

#### 存活性检查 (Liveness)
- 检查配置是否加载
- 检查基本服务配置
- 检查关键配置项
- 检查事件循环状态
- 返回 200 (成功) 或 500 (失败)

#### 就绪性检查 (Readiness)
- 执行所有存活性检查项目
- 检查服务器实例状态
- 检查关键模块配置
- 检查必要的配置项
- 检查异步任务状态
- 返回 200 (就绪) 或 503 (未就绪)

### 响应格式

#### 成功响应示例
```json
{
  "status": "ok",
  "message": "服务存活正常",
  "timestamp": 296419.325444595
}
```

#### 失败响应示例
```json
{
  "status": "error",
  "message": "存活性检查失败: 配置未加载",
  "timestamp": 296419.325444595
}
```

## 测试

### 运行测试

1. **独立逻辑测试**（不需要服务器运行）：
   ```bash
   python simple_health_test.py
   ```

2. **端点测试**（需要服务器运行）：
   ```bash
   # 先启动服务器
   python app.py
   
   # 在另一个终端运行测试
   python test_health_check.py
   ```

### 手动测试

当服务器运行时，可以直接访问健康检查端点：

```bash
# 测试存活性探针
curl http://localhost:9902/k8s_liveiness

# 测试就绪性探针
curl http://localhost:9902/k8s_readiness
```

## 部署注意事项

### Docker 部署

确保在 Dockerfile 中暴露健康检查端口：

```dockerfile
EXPOSE 9902
```

### Kubernetes 部署

1. 确保 Service 配置包含健康检查端口
2. 根据应用启动时间调整 `initialDelaySeconds`
3. 根据应用响应时间调整 `timeoutSeconds`
4. 根据检查频率需求调整 `periodSeconds`

### 监控和日志

- 健康检查请求会记录在应用日志中
- 可以通过日志监控健康检查的执行情况
- 建议设置适当的日志级别以避免过多的健康检查日志

## 故障排除

### 常见问题

1. **端口冲突**
   - 检查 9902 端口是否被其他服务占用
   - 可以在配置文件中修改 `health_port` 设置

2. **健康检查失败**
   - 查看应用日志了解具体失败原因
   - 检查配置文件是否正确
   - 确认所有必要的服务组件都已启动

3. **超时问题**
   - 增加 Kubernetes 探针的 `timeoutSeconds`
   - 检查服务器性能和负载情况

### 调试命令

```bash
# 检查端口是否监听
netstat -tlnp | grep 9902

# 检查健康检查端点响应
curl -v http://localhost:9902/k8s_liveiness
curl -v http://localhost:9902/k8s_readiness

# 查看应用日志
tail -f tmp/server.log
```

## 扩展和自定义

### 添加自定义检查

可以在 `HealthHandler` 类中添加更多的健康检查逻辑：

```python
async def _check_database_connection(self):
    """检查数据库连接"""
    # 自定义数据库连接检查逻辑
    pass

async def _check_external_services(self):
    """检查外部服务依赖"""
    # 自定义外部服务检查逻辑
    pass
```

### 配置不同的检查级别

可以根据需要配置不同严格程度的健康检查：

```yaml
health_check:
  liveness_level: basic    # basic, standard, strict
  readiness_level: strict  # basic, standard, strict
```

这样的实现为 Kubernetes 部署提供了完整的健康检查支持，确保应用程序的高可用性和可靠性。
