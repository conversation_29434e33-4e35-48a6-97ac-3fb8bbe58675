import json
import asyncio
import logging
from aiohttp import web

TAG = __name__


class HealthHandler:
    """Kubernetes健康检查处理器"""

    def __init__(self, config: dict, server_instance=None):
        self.config = config
        self.server_instance = server_instance
        # 使用标准logging避免配置依赖问题
        self.logger = logging.getLogger(TAG)
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def _add_cors_headers(self, response):
        """添加CORS头"""
        response.headers["Access-Control-Allow-Origin"] = "*"
        response.headers["Access-Control-Allow-Methods"] = "GET, POST, OPTIONS"
        response.headers["Access-Control-Allow-Headers"] = "Content-Type, Authorization"
        return response
    
    async def handle_liveness(self, request):
        """处理存活性探针请求

        存活性探针用于检查应用程序是否还在运行。
        如果存活性探针失败，Kubernetes会重启容器。
        """
        return self.handle_readiness(self)
    
    async def handle_readiness(self, request):
        """处理就绪性探针请求

        就绪性探针用于检查应用程序是否准备好接收流量。
        如果就绪性探针失败，Kubernetes会将Pod从Service的端点中移除。
        """
        response_data = {
            "status": "ready",
            "message": "服务就绪正常",
            "timestamp": asyncio.get_event_loop().time(),
        }

        return web.Response(
            text=json.dumps(response_data, ensure_ascii=False),
            content_type="application/json",
            status=200,
            charset="utf-8"
        )

