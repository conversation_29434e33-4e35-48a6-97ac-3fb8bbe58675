import asyncio
from aiohttp import web
from config.logger import setup_logging
from core.api.ota_handler import OTAHandler
from core.api.vision_handler import VisionHandler
from core.api.health_handler import HealthHandler

TAG = __name__


class SimpleHttpServer:
    def __init__(self, config: dict, server_instance=None):
        self.config = config
        self.server_instance = server_instance
        self.logger = setup_logging()
        self.ota_handler = OTAHandler(config)
        self.vision_handler = VisionHandler(config)
        self.health_handler = HealthHandler(config, server_instance)

    def _get_websocket_url(self, local_ip: str, port: int) -> str:
        """获取websocket地址

        Args:
            local_ip: 本地IP地址
            port: 端口号

        Returns:
            str: websocket地址
        """
        server_config = self.config["server"]
        websocket_config = server_config.get("websocket")

        if websocket_config and "你" not in websocket_config:
            return websocket_config
        else:
            return f"ws://{local_ip}:{port}/xiaozhi/v1/"

    async def start(self):
        server_config = self.config["server"]
        host = server_config.get("ip", "0.0.0.0")
        port = int(server_config.get("http_port", 8003))
        health_port = int(server_config.get("health_port", 9902))

        # 启动主HTTP服务器
        main_server_task = None
        if port:
            main_server_task = asyncio.create_task(self._start_main_server(host, port))

        # 启动健康检查服务器
        health_server_task = asyncio.create_task(self._start_health_server(host, health_port))

        # 等待所有服务器启动
        tasks = [health_server_task]
        if main_server_task:
            tasks.append(main_server_task)

        try:
            await asyncio.gather(*tasks)
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"HTTP服务器启动失败: {e}")
            raise

    async def _start_main_server(self, host: str, port: int):
        """启动主HTTP服务器"""
        app = web.Application()
        server_config = self.config["server"]
        read_config_from_api = server_config.get("read_config_from_api", False)

        if not read_config_from_api:
            # 如果没有开启智控台，只是单模块运行，就需要再添加简单OTA接口，用于下发websocket接口
            app.add_routes(
                [
                    web.get("/xiaozhi/ota/", self.ota_handler.handle_get),
                    web.post("/xiaozhi/ota/", self.ota_handler.handle_post),
                    web.options("/xiaozhi/ota/", self.ota_handler.handle_post),
                ]
            )
        # 添加路由
        app.add_routes(
            [
                web.get("/mcp/vision/explain", self.vision_handler.handle_get),
                web.post("/mcp/vision/explain", self.vision_handler.handle_post),
                web.options("/mcp/vision/explain", self.vision_handler.handle_post),
            ]
        )

        # 运行主服务
        runner = web.AppRunner(app)
        await runner.setup()
        site = web.TCPSite(runner, host, port)
        await site.start()

        self.logger.bind(tag=TAG).info(f"主HTTP服务器启动在 {host}:{port}")

        # 保持服务运行
        while True:
            await asyncio.sleep(3600)  # 每隔 1 小时检查一次

    async def _start_health_server(self, host: str, port: int):
        """启动健康检查服务器"""
        health_app = web.Application()

        # 添加健康检查路由
        health_app.add_routes(
            [
                web.get("/k8s_liveiness", self.health_handler.handle_liveness),
                web.get("/k8s_readiness", self.health_handler.handle_readiness),
            ]
        )

        # 运行健康检查服务
        health_runner = web.AppRunner(health_app)
        await health_runner.setup()
        health_site = web.TCPSite(health_runner, host, port)
        await health_site.start()

        self.logger.bind(tag=TAG).info(f"健康检查服务器启动在 {host}:{port}")

        # 保持服务运行
        while True:
            await asyncio.sleep(3600)  # 每隔 1 小时检查一次
