#!/usr/bin/env python3
"""
简单的健康检查测试，不依赖完整的配置系统
"""

import asyncio
import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.api.health_handler import HealthHandler


async def test_health_logic():
    """测试健康检查逻辑"""
    print("简单健康检查逻辑测试")
    print("=" * 50)
    
    # 创建测试配置
    test_config = {
        "server": {
            "ip": "0.0.0.0",
            "port": 8000,
            "http_port": 8003,
            "health_port": 9902
        },
        "selected_module": {
            "VAD": "SileroVAD",
            "ASR": "SenseVoiceASR", 
            "LLM": "OpenAILLM",
            "TTS": "EdgeTTS"
        },
        "xiaozhi": {
            "name": "小智",
            "version": "1.0"
        }
    }
    
    # 创建健康检查处理器
    health_handler = HealthHandler(test_config)
    
    # 模拟请求对象
    class MockRequest:
        pass
    
    mock_request = MockRequest()
    
    # 测试存活性检查
    print("1. 测试存活性检查逻辑")
    try:
        response = await health_handler.handle_liveness(mock_request)
        print(f"   状态码: {response.status}")
        print(f"   内容类型: {response.content_type}")
        
        # 读取响应内容
        body = response.body
        if body:
            try:
                data = json.loads(body.decode('utf-8'))
                print(f"   响应内容: {json.dumps(data, ensure_ascii=False, indent=2)}")
            except:
                print(f"   响应内容: {body.decode('utf-8')}")
        
        if response.status == 200:
            print("   ✅ 存活性检查逻辑正常")
        else:
            print("   ❌ 存活性检查逻辑异常")
            
    except Exception as e:
        print(f"   ❌ 存活性检查逻辑测试异常: {e}")
        import traceback
        traceback.print_exc()
    
    print()
    
    # 测试就绪性检查
    print("2. 测试就绪性检查逻辑")
    try:
        response = await health_handler.handle_readiness(mock_request)
        print(f"   状态码: {response.status}")
        print(f"   内容类型: {response.content_type}")
        
        # 读取响应内容
        body = response.body
        if body:
            try:
                data = json.loads(body.decode('utf-8'))
                print(f"   响应内容: {json.dumps(data, ensure_ascii=False, indent=2)}")
            except:
                print(f"   响应内容: {body.decode('utf-8')}")
        
        if response.status in [200, 503]:
            print("   ✅ 就绪性检查逻辑正常")
        else:
            print("   ❌ 就绪性检查逻辑异常")
            
    except Exception as e:
        print(f"   ❌ 就绪性检查逻辑测试异常: {e}")
        import traceback
        traceback.print_exc()
    
    print()
    print("=" * 50)
    print("简单测试完成")


if __name__ == "__main__":
    asyncio.run(test_health_logic())
