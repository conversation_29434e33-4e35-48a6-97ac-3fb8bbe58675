# Git相关
.git
.gitignore
.gitattributes

# Python相关
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虚拟环境
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE相关
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统相关
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 日志文件
*.log
logs/
tmp/*.log

# 测试相关
test/
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# 文档
docs/
*.md
README*
CHANGELOG*
LICENSE*

# 配置文件（保留必要的）
.env
.env.local
.env.*.local

# 临时文件
tmp/
temp/
*.tmp
*.temp

# 音乐文件（如果不需要）
music/
*.mp3
*.wav
*.flac

# 模型文件（如果太大可以考虑排除）
# models/

# Docker相关
Dockerfile*
docker-compose*.yml
.dockerignore

# CI/CD相关
Jenkinsfile
.github/
.gitlab-ci.yml
Makefile

# 性能测试
performance_tester*.py

# 数据目录（运行时创建）
data/

# 其他不必要的文件
*.bak
*.orig
*.rej
.mypy_cache/
.dmypy.json
dmypy.json
.pyre/
.pytype/
cython_debug/
