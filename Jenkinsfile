@Library('JenkinsLibrary') _
def map = [:]
def jobName = env.JOB_NAME
def appName = jobName.substring(jobName.lastIndexOf("/") + 1, jobName.length())
def dirName = jobName.split('/')[0];
/* 微服务名称 */
map.put('microServiceName',appName)
/* 业务线目录 */
map.put('dirName',dirName)

/* 以下内容请各位根据项目实际情况填写  */
/* gitURL  根据实际情况填写 仅支持以[https://git.daddylab.com/]为前缀  */
map.put('gitURL','https://git.daddylab.com/mimo/mimo-server.git')

/* 是否开启代码扫描 */
map.put('codeScan',"false")

/* 指定DockerFile */
map.put('dockerfile',"Dockerfile/backEnd/mimo-server-Dockerfile")

/* 指定Makefile路径 如果为空代表项目的根目录(只填目录即可) */
map.put('makefilePath',"")

/* 指定make编译命令 前端建议填写[make build]即可,会兼容原构建方式 */
map.put('makeCmd',"make build")

/* 是否运行自己的骚操作 */
map.put('buildshFlag',"false")

/* 自定义操作为 build.sh 脚本，可自行定义脚本里面的内容，但是文件名只能是[build.sh] */
/* 指定buildshPath路径 如果为空代表项目的根目录(只填目录即可) */
map.put('buildshPath',"")

argocdBuildPipeline(map)

