FROM harbor.dlab.cn/public/library/python:3.10-slim

# 更换软件源
RUN echo '\
deb https://mirrors.tuna.tsinghua.edu.cn/debian/ bookworm main contrib non-free\n\
deb https://mirrors.tuna.tsinghua.edu.cn/debian/ bookworm-updates main contrib non-free\n\
deb https://mirrors.tuna.tsinghua.edu.cn/debian/ bookworm-backports main contrib non-free\n\
deb https://security.debian.org/debian-security bookworm-security main contrib non-free\
' > /etc/apt/sources.list && \
apt-get clean && apt-get update

# 安装系统依赖
RUN apt-get install -y --no-install-recommends libopus0 ffmpeg && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

ADD requirements-base.txt requirements.txt
# docker build -f Dockerfile-mimo-python-310-base -t mimo/python:3.10-base ./
RUN --mount=type=cache,target=/root/.cache/pip pip install --cache-dir /root/.cache/pip -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
